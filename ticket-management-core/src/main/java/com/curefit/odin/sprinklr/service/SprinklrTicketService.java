package com.curefit.odin.sprinklr.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.iris.enums.DeeplinkServices;
import com.curefit.iris.models.DeepLinkRequestPayload;
import com.curefit.iris.models.DeepLinkResponse;
import com.curefit.iris.models.LinkPayload;
import com.curefit.iris.services.spi.DeepLinkService;
import com.curefit.odin.api.SprinklrExternalClient;
import com.curefit.odin.enums.RashiEventType;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.sprinklr.external.RashiHelper;
import com.curefit.odin.sprinklr.models.RashiSprinklrTicketEvent;
import com.curefit.odin.sprinklr.models.SprinklrTicket;
import com.curefit.odin.sprinklr.pojo.SprinklrCultConfigurationEntry;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketUpdateRequest;
import com.curefit.odin.sprinklr.pojo.message.*;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrCommentWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrTicketWebhookPayload;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrTicketWebhookRequest;
import com.curefit.odin.sprinklr.repositories.SprinklrTicketRepository;
import com.curefit.odin.user.pojo.CommentEntry;
import com.curefit.odin.user.service.CommentService;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.utils.AsyncService;
import com.curefit.odin.utils.OdinStringUtils;
import com.curefit.odin.utils.SprinklrUtils;
import com.curefit.odin.utils.URLUtils;
import com.curefit.reportissues.dto.NoShowAutoResolveRequest;
import com.curefit.reportissues.models.NoShowAutoResolveResponse;
import com.curefit.reportissues.services.SprinklrAutoResolveService;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.SprinklrConstants.*;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SprinklrTicketService extends BaseMySQLService<SprinklrTicket, SprinklrTicketEntry> {

    @Autowired
    SprinklrCultCustomFieldMappingService sprinklrCultCustomFieldMappingService;

    @Autowired
    SprinklrCultConfigurationService sprinklrCultConfigurationService;

    @Autowired
    SprinklrAutoResolveService sprinklrAutoResolveService;

    @Autowired
    SprinklrExternalClient sprinklrExternalClient;

    @Autowired
    TicketService ticketService;

    @Autowired
    CommentService commentService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    DeepLinkService deepLinkService;

    @Autowired
    RashiHelper rashiHelper;

    @Autowired
    UserServiceClient userServiceClient;

    @Autowired
    SprinklrSchedulingService sprinklrSchedulingService;

    public SprinklrTicketService(SprinklrTicketRepository sprinklrTicketRepository) {
        super(sprinklrTicketRepository);
    }

    public void processSprinklrWebhook(SprinklrTicketWebhookRequest sprinklrTicketWebhookRequest) throws Exception {
        log.info("processSprinklrWebhook::webhook request: {}", sprinklrTicketWebhookRequest);
        SprinklrTicketEntry sprinklrTicketEntry = convertWebhookToSprinklrTicketEntry(sprinklrTicketWebhookRequest.getPayload());
        if (sprinklrTicketEntry == null || sprinklrTicketEntry.getUserId() == null) {
            String errorMsg = String.format("Couldn't find curefit userId in the webhook request for id: %s, caseNumber: %s", sprinklrTicketWebhookRequest.getId(), sprinklrTicketWebhookRequest.getPayload().getCaseNumber());
            log.error(errorMsg);
            return;
            // throw new BaseException(errorMsg, AppStatus.BAD_REQUEST, LogType.WARNING);
        }
        SprinklrTicketEntry oldSprinklrEntry = getByCaseNumber(sprinklrTicketEntry.getCaseNumber()).orElse(null);
        sprinklrTicketEntry = createOrUpdateSprinklrTicket(sprinklrTicketEntry);
        SprinklrTicketEntry finalSprinklrTicketEntry = sprinklrTicketEntry;
        AsyncService.submit(() -> processCustomProperties(finalSprinklrTicketEntry));
        AsyncService.submit(() -> processOdinCustomProperties(finalSprinklrTicketEntry, oldSprinklrEntry));
    }

    private void processOdinCustomProperties(SprinklrTicketEntry sprinklrTicketEntry, SprinklrTicketEntry oldSprinklrEntry) {
        if (sprinklrTicketEntry.getOdinTicketId() == null) {
            log.info("odin ticketId isn't present for sprinklr case number: {}", sprinklrTicketEntry.getCaseNumber());
            return;
        }
        log.info("Odin ticketId: {} is present in case number: {}", sprinklrTicketEntry.getOdinTicketId(), sprinklrTicketEntry.getCaseNumber());
        Map<String, List<Object>> cultCustomProperties = getCultCustomProperties(sprinklrTicketEntry.getCustomProperties());
        Map<String, List<Object>> oldCustomProperties = oldSprinklrEntry != null ? getCultCustomProperties(oldSprinklrEntry.getCustomProperties()) : new HashMap<>();
        if (reopenOdinTicket(cultCustomProperties, oldCustomProperties)) {
            try {
                ticketService.updateStatus(Long.valueOf(sprinklrTicketEntry.getOdinTicketId()), Status.OPEN);
            } catch (Exception e) {
                log.error("Error while reopen the odin ticket for sprinklr case number: {}, error: {}", sprinklrTicketEntry.getCaseNumber(), e.getMessage(), e);
            }
            return;
        }
        if (closeOdinTicket(cultCustomProperties)) {
            try {
                ticketService.updateStatus(Long.valueOf(sprinklrTicketEntry.getOdinTicketId()), Status.RESOLVED);
            } catch (Exception e) {
                log.error("Error while resolving the odin ticket for sprinklr case number: {}, error: {}", sprinklrTicketEntry.getCaseNumber(), e.getMessage(), e);
            }
        }
    }

    private void processCustomProperties(SprinklrTicketEntry sprinklrTicketEntry) {
        Map<String, List<Object>> cultCustomProperties = getCultCustomProperties(sprinklrTicketEntry.getCustomProperties());
        if (!checkIfAutomationProcessingRequired(cultCustomProperties)) {
            log.info("Automation processing not required for caseNumber: {}", sprinklrTicketEntry.getCaseNumber());
            return;
        }
        try {
            processLiveChatProperties(cultCustomProperties, sprinklrTicketEntry);
        } catch (BaseException e) {
            log.error("Error while processing live chat properties: {} for ticketId {}", e.getMessage(), sprinklrTicketEntry.getCaseNumber(), e);
        }
    }

    private void processLiveChatProperties(Map<String, List<Object>> cultCustomProperties, SprinklrTicketEntry sprinklrTicketEntry) throws BaseException {
        String caseNumber = sprinklrTicketEntry.getCaseNumber();
        log.info("process Live Chat Properties for caseNumber: {}", caseNumber);
        if (cultCustomProperties.containsKey(CULT_LIVE_CHAT_L1_FIELD) && cultCustomProperties.containsKey(CULT_LIVE_CHAT_L2_FIELD)) {

            String liveChatL1 = String.valueOf(cultCustomProperties.get(CULT_LIVE_CHAT_L1_FIELD).getFirst());
            String liveChatSubL1 = CULT_ACCOUNT_SUB_L1_VALUE;
            String liveChatL2 = String.valueOf(cultCustomProperties.get(CULT_LIVE_CHAT_L2_FIELD).getFirst());
            if (cultCustomProperties.containsKey(CULT_LIVE_CHAT_SUB_L1_FIELD)) {
                liveChatSubL1 = String.valueOf(cultCustomProperties.get(CULT_LIVE_CHAT_SUB_L1_FIELD).getFirst());
            }

            String statusField = null;
            if (CULT_SESSION.equals(liveChatL1)) {
                statusField = CULT_SESSION_STATUS_FIELD;
            } else if (CULT_MEMBERSHIP.equals(liveChatL1)) {
                statusField = CULT_MEMBERSHIP_STATUS_FIELD;
            }
            log.info("processLiveChatProperties: fields value for caseNumber: {}, liveChatL1: {}, liveChatSubL1: {}, liveChatL2: {}, statusField: {}", caseNumber, liveChatL1, liveChatSubL1, liveChatL2, statusField);
            String status = CULT_ACCOUNT_STATUS_VALUE;
            if (statusField != null && cultCustomProperties.containsKey(statusField)) {
                status = String.valueOf(cultCustomProperties.get(statusField).getFirst());
            }
            if (status != null) {
                SprinklrCultConfigurationEntry configuration = sprinklrCultConfigurationService.getSprinklrCultConfiguration(liveChatL1, liveChatSubL1, status, liveChatL2);
                if (configuration == null || configuration.getActionValue() == null) {
                    log.error("Couldn't find SprinklrCultConfigurationEntry for l1: {}, subL1: {}, status: {}, l2: {} for caseNumber: {}", liveChatL1, liveChatSubL1, status, liveChatL2, caseNumber);
                    return;
                }
                log.info("processLiveChatProperties: caseNumber: {}, configuration: {}", caseNumber, configuration);
                Map<String, List<Object>> syncedSelectedCultCustomPropertiesToBeUpdated = new HashMap<>();
                switch (configuration.getActionType()) {
                    case WEB_LINK:
                        syncedSelectedCultCustomPropertiesToBeUpdated.put(CULT_AUTOMATION_DEEP_LINK, List.of(configuration.getActionValue().getValue()));
                        break;
                    case DEEP_LINK:
                        String deepLink = configuration.getActionValue().getValue();
                        Map<String, String> params = new HashMap<>();
                        if (!CollectionUtils.isEmpty(configuration.getActionValue().getParams())) {
                            configuration.getActionValue().getParams().forEach(obj -> {
                                if (cultCustomProperties.containsKey(obj.getCultCustomFieldMapping())) {
                                    String paramValue = String.valueOf(cultCustomProperties.get(obj.getCultCustomFieldMapping()).getFirst());
                                    params.put(obj.getParamKey(), paramValue);
                                }
                            });
                        }
                        String finalDeepLink = URLUtils.addParamsToUrl(deepLink, params);
                        syncedSelectedCultCustomPropertiesToBeUpdated.put(CULT_AUTOMATION_DEEP_LINK, List.of(finalDeepLink));
                        break;
                    case NO_SHOW_AUTOMATION:
                        // call report issue client
                        NoShowAutoResolveRequest requestData = new NoShowAutoResolveRequest();
                        configuration.getActionValue().getParams().forEach(obj -> {
                            if (cultCustomProperties.containsKey(obj.getCultCustomFieldMapping())) {
                                String paramValue = String.valueOf(cultCustomProperties.get(obj.getCultCustomFieldMapping()).getFirst());
                                try {
                                    BeanUtils.setProperty(requestData, obj.getParamKey(), paramValue);
                                } catch (Exception e) {
                                    throw new RuntimeException("Error setting field dynamically: " + obj.getParamKey(), e);
                                }
                            }
                        });
                        try {
                            NoShowAutoResolveResponse noShowResponse = sprinklrAutoResolveService.findNoShowActions(requestData);
                            if (noShowResponse.getAutomationFlag()) {
                                Long templateId = noShowResponse.getTemplateId();
                                syncedSelectedCultCustomPropertiesToBeUpdated.put(CULT_AUTOMATION_NO_SHOW_TEMPLATE_ID, List.of(templateId));
                            } else {
                                syncedSelectedCultCustomPropertiesToBeUpdated.put(CULT_AUTOMATION_FLAG_FIELD, List.of(false));
                            }
                        } catch (Exception e) {
                            String errMsg = String.format("Error while getting no show actions from report issue for request: %s, errMsg: %s", requestData, e.getMessage());
                            log.error(errMsg, e);
                        }
                        break;
                    case SCHEDULING_AUTOMATION:
                        Long centerId =(Long)(cultCustomProperties.get(CUREFIT_SCHEDULING_CENTER_ID).getFirst());
                        Long workoutId =(Long)(cultCustomProperties.get(CUREFIT_SCHEDULING_WORKOUT_ID).getFirst());
                        Long timeSlotId =(Long)(cultCustomProperties.get(CUREFIT_SCHEDULING_TIME_SLOT_ID).getFirst());
                        String schedulingFlag = "0";
                        if (centerId != null && workoutId != null && timeSlotId != null && StringUtils.hasLength(sprinklrTicketEntry.getUserId())) {
                            try {
                                schedulingFlag = sprinklrSchedulingService.getSchedulingTicketCreateFlag(centerId, workoutId, timeSlotId, sprinklrTicketEntry.getUserId());
                            } catch (Exception e) {
                                log.error("Error while getting scheduling ticket create flag for centerId: {}, workoutId: {}, timeSlotId: {}, error: {}", centerId, workoutId, timeSlotId, e.getMessage(), e);
                            }
                        }
                        syncedSelectedCultCustomPropertiesToBeUpdated.put(SCHEDULING_FLAG, List.of(schedulingFlag));
                        break;
                    default:
                        log.info("Handling is missing for actionType: {}", configuration.getActionType());
                        break;
                }
                if (StringUtils.hasLength(configuration.getImage())) {
                    syncedSelectedCultCustomPropertiesToBeUpdated.put(CULT_AUTOMATION_DEEP_LINK_PREVIEW_IMAGE, List.of(configuration.getImage()));
                }
                log.info("processLiveChatProperties: caseNumber: {}, propertiesToUpdate: {}", caseNumber, syncedSelectedCultCustomPropertiesToBeUpdated);
                updateExternalSprinklrCase(sprinklrTicketEntry.getCaseNumber(), syncedSelectedCultCustomPropertiesToBeUpdated);
            }
        }
    }

    public void updateExternalSprinklrCase(String caseNumber, Map<String, List<Object>> syncedSelectedCultCustomPropertiesToBeUpdated) {
        Map<String, List<Object>> sprinklrCustomProperties = getSprinklrCustomProperties(syncedSelectedCultCustomPropertiesToBeUpdated);
        log.info("updateExternalSprinklrCase for caseNumber: {}, cultProperties: {},  sprinklrProperties: {}", caseNumber, syncedSelectedCultCustomPropertiesToBeUpdated, sprinklrCustomProperties);
        if (!sprinklrCustomProperties.isEmpty()) {
            int retryCount = 0;
            while (retryCount < 5) {
                try {
                    SprinklrTicketUpdateRequest sprinklrTicketUpdateRequest = SprinklrTicketUpdateRequest.builder()
                            .caseNumbers(List.of(caseNumber))
                            .syncedSelectedCustomProperties(sprinklrCustomProperties)
                            .updateActions(SPRINKLR_UPDATE_TICKET_ACTIONS)
                            .build();
                    log.info("updateExternalSprinklrCase for caseNumber: {} updated sprinklr case request: {}, retryCount: {}", caseNumber, sprinklrTicketUpdateRequest, retryCount + 1);
                    Object updatedSprinklrCase = sprinklrExternalClient.updateSprinklrCase(sprinklrTicketUpdateRequest);
                    log.info("updateExternalSprinklrCase for caseNumber: {}, sprinklr response: {}, retryCount: {}", caseNumber, updatedSprinklrCase.toString(), retryCount + 1);
                    return;
                } catch (Exception e) {
                    String errMsg = String.format("updateExternalSprinklrCase failure for caseNumber: %s on attempt %d - Error: %s", caseNumber, retryCount + 1, e.getMessage());
                    log.error(errMsg, e);
                    retryCount++;
                    try {
                        Thread.sleep(300);
                    } catch (InterruptedException ex) {
                        log.error("updateExternalSprinklrCase::Retry sleep interrupted. Aborting retries. Retry count: {}", retryCount + 1);
                        break;
                    }
                }
            }
            rollbarService.error(String.format("updateExternalSprinklrCase failure for caseNumber: %s even after all the retries", caseNumber));
        }
    }

    private Boolean checkIfAutomationProcessingRequired(Map<String, List<Object>> cultCustomProperties) {
        if (cultCustomProperties.containsKey(CULT_AUTOMATION_FLAG_FIELD)) {
            Object value = cultCustomProperties.get(CULT_AUTOMATION_FLAG_FIELD).getFirst();
            return Boolean.valueOf(String.valueOf(value).toLowerCase());
        }
        return false;
    }

    private Map<String, List<Object>> getCultCustomProperties(Map<String, List<Object>> sprinklrCustomProperties) {
        Map<String, String> sprinklrVsCultMapping = sprinklrCultCustomFieldMappingService.sprinklrVsCultMapping();
        Map<String, List<Object>> cultCustomProperties = new HashMap<>();
        for (String sprinklrField : sprinklrCustomProperties.keySet()) {
            cultCustomProperties.put(sprinklrVsCultMapping.get(sprinklrField), sprinklrCustomProperties.get(sprinklrField));
        }
        return cultCustomProperties;
    }

    private Map<String, List<Object>> getSprinklrCustomProperties(Map<String, List<Object>> cultCustomProperties) {
        Map<String, String> cultVsSprinklrMapping = sprinklrCultCustomFieldMappingService.cultVsSprinklrMapping();
        Map<String, List<Object>> sprinklrCustomProperties = new HashMap<>();
        for (String cultField : cultCustomProperties.keySet()) {
            sprinklrCustomProperties.put(cultVsSprinklrMapping.get(cultField), cultCustomProperties.get(cultField));
        }
        return sprinklrCustomProperties;
    }

    private SprinklrTicketEntry createOrUpdateSprinklrTicket(SprinklrTicketEntry sprinklrTicketEntry) throws BaseException {
        Optional<SprinklrTicketEntry> existingEntry = getByCaseNumber(sprinklrTicketEntry.getCaseNumber());
        if (existingEntry.isEmpty()) {
            return create(sprinklrTicketEntry);
        }
        return patchUpdate(existingEntry.get().getId(), sprinklrTicketEntry);
    }

    @Override
    public SprinklrTicketEntry patchUpdate(Long id, SprinklrTicketEntry sprinklrTicketEntry) throws BaseException {
        SprinklrTicketEntry existingEntry = findOneById(id);
        //mergeCustomProperties(sprinklrTicketEntry, existingEntry);
        SprinklrTicketEntry updatedEntry = super.patchUpdate(id, sprinklrTicketEntry);
        AsyncService.submit(() -> postUpdateHook(updatedEntry, existingEntry));
        return updatedEntry;
    }

    private void mergeCustomProperties(SprinklrTicketEntry newTicketEntry, SprinklrTicketEntry existingEntry) {
        if (existingEntry.getCustomProperties() != null) {
            if (newTicketEntry.getCustomProperties() == null) {
                newTicketEntry.setCustomProperties(existingEntry.getCustomProperties());
            } else {
                // First, add all existing properties that are not in the new entry (preserve older ones)
                existingEntry.getCustomProperties()
                        .forEach((key, value) -> newTicketEntry.getCustomProperties()
                                .putIfAbsent(key, new ArrayList<>(value)));
                // Note: For matching keys, newTicketEntry already has the latest values, so no additional action needed
            }
        }
    }

    private void postUpdateHook(SprinklrTicketEntry updatedEntry, SprinklrTicketEntry existingEntry) {
        if (BooleanUtils.isTrue(updatedEntry.getIsTicket())) {
            postUpdateTicketHook(updatedEntry, existingEntry);
        }
    }

    private void postUpdateTicketHook(SprinklrTicketEntry updatedEntry, SprinklrTicketEntry existingEntry) {
        String updatedStatus = SprinklrUtils.getTicketStatus(updatedEntry).getText();
        String existingStatus = SprinklrUtils.getTicketStatus(existingEntry).getText();
        if (!existingStatus.equals(updatedStatus)) {
            RashiEventType eventType = SprinklrUtils.getRashiEventType(updatedStatus);
            if (eventType != null) {
                enqueueSprinklrTicketEvent(updatedEntry, eventType);
            }
        }
    }

    private void enqueueSprinklrTicketEvent(SprinklrTicketEntry sprinklrTicketEntry, RashiEventType rashiEventType) {
        try {
            RashiSprinklrTicketEvent rashiSprinklrTicketEvent = RashiSprinklrTicketEvent.builder()
                    .caseNumber(sprinklrTicketEntry.getCaseNumber())
                    .status(SprinklrUtils.getTicketStatus(sprinklrTicketEntry).getText())
                    .subject(sprinklrTicketEntry.getSubject())
                    .build();
            rashiHelper.enqueueSprinklrTicketEventAsync(rashiEventType, Long.valueOf(sprinklrTicketEntry.getUserId()), rashiSprinklrTicketEvent, new Date(), "CUREFIT");
        } catch (NumberFormatException e) {
            log.error("Error while sending events to rashi for caseNumber: {}", sprinklrTicketEntry.getCaseNumber());
        }
    }

    @Override
    public SprinklrTicketEntry create(SprinklrTicketEntry sprinklrTicketEntry) throws BaseException {
        sprinklrTicketEntry = super.create(sprinklrTicketEntry);
        SprinklrTicketEntry finalSprinklrTicketEntry = sprinklrTicketEntry;
        AsyncService.submit(() -> postCreationHook(finalSprinklrTicketEntry));
        return sprinklrTicketEntry;
    }

    void postCreationHook(SprinklrTicketEntry sprinklrTicketEntry) {
        if (BooleanUtils.isTrue(sprinklrTicketEntry.getIsTicket())) {
            postCreationTicketHook(sprinklrTicketEntry);
        }
    }

    void postCreationTicketHook(SprinklrTicketEntry sprinklrTicketEntry) {
        log.info("postCreationHook is invoked for sprinklr caseNumber: {}", sprinklrTicketEntry.getCaseNumber());
        String universalLink = null;
        DeepLinkRequestPayload payload = new DeepLinkRequestPayload();
        LinkPayload linkpayload = new LinkPayload();
        linkpayload.setApplink(CULT_CSAT_DEEPLINK + sprinklrTicketEntry.getCaseNumber());
        payload.setLink(linkpayload);
        payload.setPreferredService(DeeplinkServices.BRANCH);
        try {
            DeepLinkResponse response = deepLinkService.createUniversalDeepLinkUrl(payload);
            universalLink = response.getUrl();
            log.info("createUniversalDeepLinkUrl::response: {}", response.getUrl());
        } catch (Exception error) {
            log.info("createUniversalDeepLinkUrl::payload: {}, error: {}", payload, error.getMessage(), error);
        }

        if (StringUtils.hasLength(universalLink)) {
            updateExternalSprinklrCase(sprinklrTicketEntry.getCaseNumber(), Map.of(CSAT_DEEPLINK_FIELD, List.of(universalLink)));
        }

        enqueueSprinklrTicketEvent(sprinklrTicketEntry, RashiEventType.SPRINKLR_TICKET_CREATED);
    }

    private SprinklrTicketEntry convertWebhookToSprinklrTicketEntry(SprinklrTicketWebhookPayload sprinklrTicketWebhookPayload) {
        if (sprinklrTicketWebhookPayload == null || sprinklrTicketWebhookPayload.getWorkflow() == null || CollectionUtils.isEmpty(sprinklrTicketWebhookPayload.getWorkflow().getCustomProperties())) {
            return null;
        }
        Map<String, List<Object>> cultCustomProperties = getCultCustomProperties(sprinklrTicketWebhookPayload.getWorkflow().getCustomProperties());
        return SprinklrTicketEntry.builder()
                .userId(getUserIdFromCustomProperties(cultCustomProperties))
                .caseId(sprinklrTicketWebhookPayload.getId())
                .caseNumber(String.valueOf(sprinklrTicketWebhookPayload.getCaseNumber()))
                .subject(getSubjectFromCustomProperties(cultCustomProperties))
                .description(getDescriptionFromCustomProperties(cultCustomProperties))
                .status(sprinklrTicketWebhookPayload.getStatus())
                .customProperties(sprinklrTicketWebhookPayload.getWorkflow().getCustomProperties())
                .contact(sprinklrTicketWebhookPayload.getContact())
                .firstMessageId(sprinklrTicketWebhookPayload.getFirstMessageId())
                .conversationId(sprinklrTicketWebhookPayload.getConversationId())
                .isTicket(getIsTicketFlagFromCustomProperties(cultCustomProperties))
                .odinTicketId(getOdinTicketIdFromCustomProperties(cultCustomProperties))
                .build();
    }

    private Optional<SprinklrTicketEntry> getByCaseNumber(String caseNumber) {
        return ((SprinklrTicketRepository) baseMySQLRepository).findFirstByCaseNumberOrderByIdDesc(caseNumber).map(this::convertToEntry);
    }

    private String getUserIdFromCustomProperties(Map<String, List<Object>> cultCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(CUREFIT_USER_ID)) {
                String userId = String.valueOf(cultCustomProperties.get(CUREFIT_USER_ID).getFirst());
                if (OdinStringUtils.isNumeric(userId)) {
                    return userId;
                }
            }
        }
        return null;
    }

    private Boolean closeOdinTicket(Map<String, List<Object>> cultCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(ODIN_TICKET_CLOSE_FLAG)) {
                return "Y".equalsIgnoreCase(String.valueOf(cultCustomProperties.get(ODIN_TICKET_CLOSE_FLAG).getFirst()));
            }
        }
        return false;
    }

    private Boolean reopenOdinTicket(Map<String, List<Object>> cultCustomProperties, Map<String, List<Object>> oldCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(ODIN_TICKET_REOPEN_FLAG)) {
                if ("Y".equalsIgnoreCase(String.valueOf(cultCustomProperties.get(ODIN_TICKET_REOPEN_FLAG).getFirst()))) {
                    return !oldCustomProperties.containsKey(ODIN_TICKET_REOPEN_FLAG) || !"Y".equalsIgnoreCase(String.valueOf(oldCustomProperties.get(ODIN_TICKET_REOPEN_FLAG).getFirst()));
                }
            }
        }
        return false;
    }

    private String getSubjectFromCustomProperties(Map<String, List<Object>> cultCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(AGENT_L2)) {
                return String.valueOf(cultCustomProperties.get(AGENT_L2).getFirst());
            }
        }
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(CULT_LIVE_CHAT_L2_FIELD)) {
                return String.valueOf(cultCustomProperties.get(CULT_LIVE_CHAT_L2_FIELD).getFirst());
            }
        }
        return null;
    }

    private String getDescriptionFromCustomProperties(Map<String, List<Object>> cultCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(DESCRIPTION)) {
                return String.valueOf(cultCustomProperties.get(DESCRIPTION).getFirst());
            }
        }
        return null;
    }

    private String getOdinTicketIdFromCustomProperties(Map<String, List<Object>> cultCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(ODIN_TICKET_ID)) {
                return String.valueOf(cultCustomProperties.get(ODIN_TICKET_ID).getFirst());
            }
        }
        return null;
    }

    private Boolean getIsTicketFlagFromCustomProperties(Map<String, List<Object>> cultCustomProperties) {
        if (!CollectionUtils.isEmpty(cultCustomProperties)) {
            if (cultCustomProperties.containsKey(IS_TICKET_FLAG)) {
                return NO.equalsIgnoreCase(String.valueOf(cultCustomProperties.get(IS_TICKET_FLAG).getFirst()));
            }
        }
        return false;
    }

    public void addMessage(SprinklrMessageCreateRequest messageCreateRequest) throws ResourceNotFoundException {
        String caseNumber = messageCreateRequest.getCaseNumber();
        Optional<SprinklrTicketEntry> ticketEntry = getByCaseNumber(caseNumber);
        if (ticketEntry.isEmpty()) {
            log.error("Couldn't found sprinklr case having case number: {}", caseNumber);
            return;
        }
        if (CollectionUtils.isEmpty(messageCreateRequest.getAttachments()) && !StringUtils.hasLength(messageCreateRequest.getBody())) {
            log.error("body and attachments are empty for case number: {}", caseNumber);
            return;
        }
        SprinklrTicketEntry sprinklrTicketEntry = ticketEntry.get();
        if (!StringUtils.hasLength(sprinklrTicketEntry.getConversationId())) {
            log.error("conversationId isn't present in sprinklr case having case number: {}", caseNumber);
            throw new ResourceNotFoundException("conversationId isn't present in sprinklr case having case number: " + sprinklrTicketEntry.getCaseNumber());
        }
        SprinklrMessageCreateRequest messagePayload = SprinklrMessageCreateRequest.builder()
                .from(messageCreateRequest.getFrom())
                .subject(StringUtils.hasLength(messageCreateRequest.getSubject()) ? messageCreateRequest.getSubject() : sprinklrTicketEntry.getSubject())
                .body(getMessageBodyHavingConversationId(messageCreateRequest.getBody(), sprinklrTicketEntry.getConversationId()))
                .attachments(messageCreateRequest.getAttachments())
                .customProperties(getMessageSentFromAPICustomProperties())
                .build();
        sprinklrExternalClient.createMessage(messagePayload, getSprinklrAIdEmail(sprinklrTicketEntry.getEmail()), messageCreateRequest.getBody());
    }

    private String getSprinklrAIdEmail(String email) {
        if (StringUtils.hasText(email)) {
            return SUPPORT_EMAIL_MAP.getOrDefault(email, CREATE_MESSAGE_A_ID_EMAIL);
        }
        return CREATE_MESSAGE_A_ID_EMAIL;
    }

    private Map<String, List<Object>> getMessageSentFromAPICustomProperties() {
        return Map.of("_c_6777a8e4c9d9737b890fbab1", List.of("Yes"));
    }

    private String getMessageBodyHavingConversationId(String body, String conversationId) {
        String appendMessage = "<span style=\"font-size: 0px; color: White; display:none\"> %%[ConversationId: " + conversationId + "]%%</span>";
        return StringUtils.hasLength(body) ? body + appendMessage : appendMessage;
    }

    public void processCommentWebhook(SprinklrCommentWebhookRequest request) {
        if (request != null && request.getPayload() != null && StringUtils.hasLength(request.getPayload().getEntityId()) && StringUtils.hasLength(request.getPayload().getText())) {
            // comment coming from sprinklr should contain odin keyword
            if (!request.getPayload().getText().toLowerCase().contains("odin")) {
                return;
            }
            Long odinTicketId = ticketService.fetchTicketIdBySourceRef(TicketSource.SPRINKLR, request.getPayload().getEntityId());
            if (odinTicketId != null) {
                CommentEntry commentEntry = new CommentEntry();
                commentEntry.setTicketId(odinTicketId);
                commentEntry.setComment(request.getPayload().getText());
                try {
                    commentService.create(commentEntry);
                } catch (BaseException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    public void reOpenTicket(@Valid SprinklrMessageCreateRequest request) throws BaseException {
        Optional<SprinklrTicketEntry> ticketEntry = getByCaseNumber(request.getCaseNumber());
        if (ticketEntry.isEmpty()) {
            log.error("Couldn't find sprinklr case having case number: {}", request.getCaseNumber());
            return;
        }
        if ("OPEN".equals(SprinklrUtils.getTicketStatus(ticketEntry.get()).getText())) {
            log.warn("Ticket is already open for case number: {}", request.getCaseNumber());
            return;
        }
        SprinklrTicketEntry update = SprinklrTicketEntry.builder().status("Open").build();
        SprinklrTicketEntry sprinklrTicketEntry = patchUpdate(ticketEntry.get().getId(), update);
        addMessage(request);
        enqueueSprinklrTicketEvent(sprinklrTicketEntry, RashiEventType.SPRINKLR_TICKET_REOPENED);
    }

    public SprinklrMessageResponse getMessageConversations(SprinklrMessageRequest request) throws BaseException {
        Optional<SprinklrTicketEntry> ticketEntry = getByCaseNumber(request.getCaseNumber());
        if (ticketEntry.isEmpty()) {
            log.error("Couldn't find Sprinklr case having case number: {}", request.getCaseNumber());
            return null;
        }
        SprinklrTicketEntry sprinklrTicketEntry = ticketEntry.get();
        if (!StringUtils.hasLength(sprinklrTicketEntry.getFirstMessageId())) {
            throw new ResourceNotFoundException("First message ID is not present for case number: " + request.getCaseNumber());
        }
        SprinklrMessageRequest sprinklrMessageRequest = SprinklrMessageRequest.builder()
                .messageId(sprinklrTicketEntry.getFirstMessageId())
                .start(request.getStart())
                .rows(request.getRows())
                .build();

        SprinklrMessageResponse messageConversations = sprinklrExternalClient.getMessageConversations(sprinklrMessageRequest);
        if (messageConversations == null) {
            return null;
        }
        populateConversationIdIfNotPresent(sprinklrTicketEntry, messageConversations);
        return parsedMessageConversations(messageConversations, sprinklrTicketEntry);
    }

    private SprinklrMessageResponse parsedMessageConversations(SprinklrMessageResponse messageConversations, SprinklrTicketEntry sprinklrTicketEntry) throws BaseException {
        if (messageConversations != null && !CollectionUtils.isEmpty(messageConversations.getData())) {
            for (SprinklrMessage sprinklrMessage : messageConversations.getData()) {
                sprinklrMessage.getContent().setText(SprinklrUtils.parseMessageText(sprinklrMessage.getContent().getText()));

                // resolve media urls
                if (BooleanUtils.isTrue(sprinklrMessage.getBrandPost()) && sprinklrMessage.getContent().getAttachment() != null && StringUtils.hasLength(sprinklrMessage.getContent().getAttachment().getUrl())) {
                    String originalUrl = sprinklrMessage.getContent().getAttachment().getUrl();
                    sprinklrMessage.getContent().getAttachment().setUrl(resolveMediaUrls(List.of(originalUrl)).get(originalUrl));
                }
            }
            Collections.reverse(messageConversations.getData());
            messageConversations.setData(filterUserConversations(messageConversations.getData(), sprinklrTicketEntry.getUserId()));
        }
        return messageConversations;
    }

    private List<SprinklrMessage> filterUserConversations(List<SprinklrMessage> data, String userId) throws BaseException {
        if (!CollectionUtils.isEmpty(data)) {
            try {
                UserEntry userEntry = userServiceClient.getUser(userId).get();
                if (userEntry != null && StringUtils.hasLength(userEntry.getEmail())) {
                    String email = userEntry.getEmail();
                    return data.stream()
                            .filter(sprinklrMessage -> email.equalsIgnoreCase(sprinklrMessage.getSenderProfile().getChannelId())
                                    || email.equalsIgnoreCase(sprinklrMessage.getReceiverProfile().getChannelId()))
                            .collect(Collectors.toList());
                }
            } catch (Exception e) {
                log.error("Error while filtering user conversations for userId: {}", userId, e);
            }
        }
        return data;
    }

    private Map<String, String> resolveMediaUrls(List<String> urls) {
        try {
            SprinklrMediaResolveResponse response = sprinklrExternalClient.resolveMedia(urls);
            return response != null ? response.getData() : Collections.emptyMap();
        } catch (Exception e) {
            log.error("Error resolving media URLs: {}", urls, e);
            return Collections.emptyMap();
        }
    }

    private void populateConversationIdIfNotPresent(SprinklrTicketEntry sprinklrTicketEntry, SprinklrMessageResponse messageConversations) throws BaseException {
        SprinklrTicketEntry sprinklrTicketToUpdate = new SprinklrTicketEntry();

        // populate conversationId
        if (!StringUtils.hasLength(sprinklrTicketEntry.getConversationId())) {
            if (!CollectionUtils.isEmpty(messageConversations.getData()) && StringUtils.hasLength(messageConversations.getData().getFirst().getConversationId())) {
                String conversationId = messageConversations.getData().getFirst().getConversationId();
                sprinklrTicketToUpdate.setConversationId(conversationId);
            } else {
                throw new ResourceNotFoundException("conversationId is not present in sprinklr fetch conversation API for case number: " + sprinklrTicketEntry.getCaseNumber());
            }
        }

        // populate support email
        if (!StringUtils.hasLength(sprinklrTicketEntry.getEmail())) {
            if (!CollectionUtils.isEmpty(messageConversations.getData())) {
                messageConversations.getData().stream()
                        .filter(SprinklrMessage::getBrandPost)
                        .findFirst()
                        .ifPresent(data -> sprinklrTicketToUpdate.setEmail(data.getSenderProfile().getChannelId()));
            } else {
                throw new ResourceNotFoundException("email is not present in sprinklr fetch conversation API for case number: " + sprinklrTicketEntry.getCaseNumber());
            }
        }

        if (sprinklrTicketToUpdate.getConversationId() != null || sprinklrTicketToUpdate.getEmail() != null) {
            patchUpdate(sprinklrTicketEntry.getId(), sprinklrTicketToUpdate);
        }
    }
}


