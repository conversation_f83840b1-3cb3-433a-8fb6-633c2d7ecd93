#spring.profiles.active=local
server.port=13000
app.environment=local
app.name=ticketing-system
app.aws.region=ap-south-1
spring.application.name=ticketing-system
spring.datasource.url=******************************************************************************
spring.datasource.username=jay_mandal
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.flyway.enabled=false
spring.flyway.baseline-on-migrate=true
spring.flyway.out-of-order=true

logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG

logfile.path=logs/ticketing-system

spring.jackson.time-zone: UTC

spring.jpa.generate-ddl=true
spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=create.sql
hibernate.hbm2ddl.delimiter=";"
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
rollbar.disabled=true
rollbar.accessToken=e2d30f547dff49cc9b1a4eec6ce187dd

spring.cache.redis.key-prefix=ODIN:
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=13151
spring.redis.database=4
spring.redis.ttl=60
spring.sleuth.propagation-keys=X-Request-Id,virtual-cluster-name
spring.sleuth.log.slf4j.whitelisted-mdc-keys=X-Request-Id,virtual-cluster-name

management.metrics.export.prometheus.enabled=false
management.metrics.export.prometheus.descriptions=false
management.metrics.enable.root=true
#management.server.address=localhost
management.endpoints.jmx.exposure.include=*
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.exclude=
management.endpoints.web.exposure.exclude=


external.user-service-v1.baseUrl=http://localhost:5001/v1
external.user-service-v2.baseUrl=http://localhost:8693/v1
mozart.base.url=http://localhost:13101/v1
neo.url=http://localhost:13102
email.enable=false
maverick.url=http://localhost:13103/platform/neo_employee_catalogue
maverick.etl.url=http://localhost:13103/etl/platform/neo_employee_catalogue

freshdesk.url=
notification.default.emails=
iris.baseUrl=http://localhost:13104
email.ticket.assign.creative=EMAIL_ODIN_TICKET_ASSIGN
email.ticket.update.creative=EMAIL_ODIN_TICKET_UPDATE
email.ticket.comment.creative=EMAIL_ODIN_TICKET_COMMENT
email.ticket.attachment.creative=EMAIL_ODIN_TICKET_ATTACHMENT
email.ticket.slaReminder.creative=EMAIL_EMAIL_ODIN_TICKET_SLA_REMINDER
email.ticket.slaToBeBreachedTomorrow.slaReminder.creative=EMAIL_EMAIL_ODIN_TICKET_SLA_TO_BE_BREACHED_TOMORROW_SLA_REMINDER
email.ticket.notification.campaign=CUREFIT_ODIN_TICKET_NOTIFICATION

attachment.s3.bucket=cf-platform-odin-stage
email.dl.refresh.cron.expression=0 55 16 ? * *
email.dl.refresh.enable=true
google.oauth.callback.url=https://localhost:13000/google/oauth/callback
google.oauth.redirect.url=http://odin.stage.curefit.co/

freshdesk.baseUrl=https://curefitsandbox.freshdesk.com
freshdesk.apiKey=${FRESHDESK_API_KEY}
freshdesk.source.email=<EMAIL>
odin.base.url=http://localhost:13105/
google.location.url=https://maps.googleapis.com/maps/api/geocode/json
google.location.api.key=${GOOGLE_LOC_API_KEY}

email.read.cron.expression=0 0/1 * 1/1 * ?
email.read.enable=true
escalation.events.queue.name=stage-platforms-odin-escalation
escalation.events.queue.batchSize=10
escalation.events.queue.waitTime=1
ticketing-system.employee.update.queue=stage-ticketing-system-employee-update
neo.employee.update.events.queue.batchSize=10
neo.employee.update.events.queue.waitTime=2
mozart.sqs.job.submit.queue=stage-platforms-mozart-job-create
mozart.job.config.id=f6edb8c7-c4e8-41d0-a901-df877e8d69e8
mozart.sla.breach.next.day.job.config.id=ticketing-system-sla-breach-next-day
email.ticket.escalation.creative=EMAIL_ODIN_TICKET_ESCALATION
google.oauth.token.directory=tokens
customer.support.email=<EMAIL>
notification.ignored.emails=<EMAIL>
crypto.key=
hr.freshdesk.baseUrl=https://curefithr.freshdesk.com
hr.freshdesk.apiKey=${HR_FRESHDESK_API_KEY}

partner.freshdesk.baseUrl=
partner.freshdesk.apiKey=

auth.enabled=false
auth.refreshIntervalMs=300000
auth.enabledUrls=
apiKey=${API_KEY}

K8S_APP_NAME=ticketing-system
audit.scan.package=com.curefit.odin
encryption.scan.package=com.curefit.odin

identity.url=http://localhost:13106
watchmen.url=http://localhost:13107

watchmen.apiKey=odin-stage-access

watchmen.membership.queue.name=stage-odin-watchmen-membership
watchmen.membership.queue.batchSize=10
watchmen.membership.queue.waitTime=1

sugarfit.freshdesk.baseUrl=
sugarfit.freshdesk.apiKey=

odin.email=<EMAIL>

shortloop.enabled=false
shortloop.url=https://k8s-shortloop.stage.curefit.co
shortloop.applicationName=ticketing-system
shortloop.environment=local

external.neo.baseUrl: http://localhost:13102
external.cult-api.baseUrl: http://localhost:13108

retry.maxAttempts: 3
retry.delay: 2000

gymfit.baseUrl=http://localhost:5001
gymfit.apiKey=

spring.autoconfigure.exclude= org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration,org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration

spring.main.allow-circular-references=true

ticketingSystem.baseUrl=http://localhost:13000
ticketingSystem.user=<EMAIL>

services.report-issues.baseUrl=http://localhost:3016
services.report-issues.apiKey=app-stage-access
crypto.cipher.transformation=AES/CTR/NoPadding

iris.requestQueue=stage-iris-campaign
services.rashi.baseUrl=https://rashi.stage.curefit.co
curefit.rashi.mongo.host=localhost
curefit.rashi.mongo.port=27017
curefit.rashi.mongo.database=micro-learning
curefit.rashi.mongo.username=${MONGO_USER:root}
curefit.rashi.mongo.password=${MONGO_PASSWORD:root}
curefit.rashi.mongo.minPoolSize=1
curefit.rashi.mongo.maxPoolSize=5
curefit.rashi.mongo.sslEnabled=false
curefit.rashi.mongo.authSource=${MONGO_AUTHSOURCE:micro-learning}
curefit.rashi.mongo.retryWrites=true
rashi.event.prefix=TICKETING_SYSTEM_
rashi.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events
curefit.rashi.redis.host=localhost
curefit.rashi.redis.port=6382
curefit.rashi.redis.clusterEnabled=false

configstore.enabled: true
configstore.apiKey: ticketing-stage-access
configstore.url: https://config-store.stage.curefit.co/
app.name=ticketing-system